# Copyright (c) 2022-2024, The ORBIT Project Developers.
# SPDX-License-Identifier: BSD-3-Clause

"""3D soil model for excavation simulation."""

from __future__ import annotations

# Ensure ExcavationEnv is not imported if unused (Linter fix)
# if TYPE_CHECKING:
#     from moleworks_ext.tasks.excavation.excavation_env_3d import ExcavationEnv

import numpy as np
import torch
import torch.profiler

# import matplotlib.pyplot as plt # Removed as it's no longer used

from ..soil_model_base import SoilModelBase
from .bucket_state_3d import BucketState3D
from .ssp_3d import SSP3D
from .soil_forces_3d import SoilForces3D
from .soil_parameters import SoilParameters
from .soil_height_3d import SoilHeight3D


# Helper function for 2D rotation
# def rot_mat_2d(angle):
#     """Creates a 2D rotation matrix."""
#     cos_a, sin_a = np.cos(angle), np.sin(angle)
#     return np.array([[cos_a, -sin_a], [sin_a, cos_a]])


class Soil3D(SoilModelBase):
    """3D soil model for excavation simulation."""

    def _zero_tensor(self, shape, dtype=None):
        return torch.zeros(shape, device=self.device, dtype=dtype or torch.float32)

    def _init_measurement_buffers(self):
        """Initialize buffers for measurements from the environment.

        This creates all the tensors needed to store positions, orientations,
        and velocities of the bucket.
        """
        # Full 3D representations - these are the source of truth
        self.bucket_pos_w = self._zero_tensor((self.n_envs, 3))
        self.prev_bucket_pos_w = self._zero_tensor((self.n_envs, 3))
        self.bucket_com_pos_w = self._zero_tensor((self.n_envs, 3))
        self.bp_unit_vector_w = self._zero_tensor((self.n_envs, 3))
        self.bucket_vel_w = self._zero_tensor((self.n_envs, 3))

        # Helper variables
        self.false_vec = self._zero_tensor((self.n_envs, 1), dtype=torch.bool)

    def __init__(self, cfg):
        # super().__init__()
        self.cfg = cfg
        self.device = cfg.sim.device
        self.n_envs = cfg.scene.num_envs
        self.dt = getattr(cfg.sim, "dt", 1 / 50)

        self.grid_size_x = cfg.soil_model_cfg.model_3d.grid_size_x
        self.grid_size_y = cfg.soil_model_cfg.model_3d.grid_size_y
        self.grid_size_z = cfg.soil_model_cfg.model_3d.grid_size_z

        self.cell_size = cfg.soil_model_cfg.model_3d.cell_size

        # Set default value for max allowed height range if not in config
        if not hasattr(cfg.soil_model_cfg.model_3d, "max_allowed_height_range"):
            cfg.soil_model_cfg.model_3d.max_allowed_height_range = (
                10.0  # Default 10m range
            )

        # Set defaults for visualization tracking
        if not hasattr(cfg.soil_model_cfg, "movement_threshold"):
            cfg.soil_model_cfg.movement_threshold = 0.05  # Default 5cm threshold

        self.enable_visualization = cfg.soil_model_cfg.enable_visualization
        self.movement_threshold = cfg.soil_model_cfg.movement_threshold
        self.prev_vis_height_field = None

        self.soil_height = SoilHeight3D(cfg, "soil_height", self.device, self.n_envs)
        self.max_depth_height = SoilHeight3D(
            cfg,
            cfg_name="max_depth_height",
            device=self.device,
            num_envs=self.n_envs,
            upper_limit=self.soil_height,
        )
        self._init_measurement_buffers()

        self.soil_parameters = SoilParameters(self, cfg)
        self.bucket_state = BucketState3D(self, cfg)
        self.ssp = SSP3D(self, cfg)
        self.forces = SoilForces3D(self, cfg)

        # Visualization settings
        self.min_update_interval = cfg.soil_model_cfg.min_update_interval
        self.update_on_excavator_action = getattr(
            cfg.soil_model_cfg, "update_on_excavator_action", True
        )

        # Track steps since last visualization update
        self.steps_since_last_update = 0

        # Track bucket position at last visualization update
        self.last_vis_update_bucket_pos = torch.zeros(
            (self.n_envs, 3), device=self.device
        )

        # Initialize processed cells mask for tracking soil updates
        self.processed_cells = torch.zeros(
            (self.n_envs, self.soil_height.grid_size_x, self.soil_height.grid_size_y),
            dtype=torch.bool,
            device=self.device,
        )

        # self.print_debug = False  # Set to False to reduce console output

    def post_init(self, env):

        self.env = env
        self.asset = env.scene.articulations["robot"]
        # self.prev_soil_height = self.soil_height.height_field.clone()

    def update(self):
        """Update the soil model.

        This method is called each simulation step.
        """
        with torch.profiler.record_function("Soil3D.update"):
            # Update soil model state
            self.update_1()
            self.update_2()

    def update_1(self, idxs=...):
        """Update the soil model state.

        This method updates measurements and bucket state.
        """
        with torch.profiler.record_function("Soil3D.update_1"):
            self._update_measurements()
            self.bucket_state.update(idxs)

            # TEMPORARILY DISABLED: Update soil height field based on bucket interaction
            # self._update_soil_height()

            # Print debug message only occasionally to reduce console spam
            # if self.steps_since_last_update % 100 == 0:
            # print("[INFO] Soil height update during digging is temporarily disabled")

            # if self.print_debug:
            #     print("[DEBUG] Soil height field stats after bucket update:")
            #     print(f"  Min: {self.soil_height.height_field.min():.3f}")
            #     print(f"  Max: {self.soil_height.height_field.max():.3f}")
            #     print(f"  Mean: {self.soil_height.height_field.mean():.3f}")
            #     # Compare with previous height field if available
            #     diff = torch.abs(self.soil_height.height_field - self.prev_soil_height)
            #     print(f"  Max change from previous: {diff.max():.3f}")
            #     print(f"  Mean change from previous: {diff.mean():.3f}")

            # Update previous height field
            # self.prev_soil_height = self.soil_height.height_field.clone()

    def update_2(self):
        with torch.profiler.record_function("Soil3D.update_2"):
            self.ssp.update()
            self.soil_parameters.update()
            self.forces.update()

    def reset(self, idxs=...):
        self.bucket_state.fill_area[idxs] = 0.0
        self.bucket_state.swept_area[idxs] = 0.0
        self.bucket_state.fill_ratio[idxs] = 0.0

        # Reset 3D soil volume tracking
        if hasattr(self.bucket_state, 'soil_volume_per_segment'):
            self.bucket_state.soil_volume_per_segment[idxs] = 0.0
        if hasattr(self.bucket_state, 'total_soil_volume'):
            self.bucket_state.total_soil_volume[idxs] = 0.0

        # Note: During reset, the soil height is still updated through the sampling process
        # in the environment's reset method, which calls soil.sample()

    def sample(self, idxs=...):
        """Sample soil parameters for specified environments.

        Args:
            idxs: Indices of environments to sample parameters for
        """
        self.soil_height.sample(idxs)
        self.max_depth_height.sample(idxs)
        self.soil_parameters.sample(idxs)

    def get_grid_bounds(self):
        """Return the bounds of the soil grid for visualization."""
        return {
            "x_min": self.soil_height.x_min,
            "x_max": self.soil_height.x_max,
            "y_min": self.soil_height.y_min,
            "y_max": self.soil_height.y_max,
            "z_min": self.soil_height.z_min,
            "z_max": self.soil_height.z_max,
        }

    def has_been_modified_since_last_update(self):
        """Check if the soil has been modified since last visualization update."""
        if (
            not hasattr(self, "prev_vis_height_field")
            or self.prev_vis_height_field is None
        ):
            self.prev_vis_height_field = self.soil_height.height_field.clone()
            return True

        # Check for significant changes
        diff = torch.abs(self.soil_height.height_field - self.prev_vis_height_field)
        significant_change = diff.max() > self.movement_threshold

        if significant_change:
            self.prev_vis_height_field = self.soil_height.height_field.clone()
            return True

        return False

    # ----- soil -----
    def get_soil_height(self):
        """Get the soil height field for visualization.

        Returns:
            Tensor of soil heights
        """
        return self.soil_height.get_height_field()

    def get_min_max_soil_height(self, idx=...):
        """Get minimum and maximum heights from the soil height field.

        Args:
            idx: Indices of environments to get heights for. Use ... for all environments.

        Returns:
            Tuple of (min_heights, max_heights) with shape [n_envs] each
        """
        # Handle empty indices case
        if isinstance(idx, torch.Tensor) and idx.numel() == 0:
            # Return empty tensors with correct shape
            return (
                torch.zeros(0, device=self.device),
                torch.zeros(0, device=self.device),
            )

        # Create a grid of points to sample heights
        x_points = torch.linspace(
            self.soil_height.x_min,
            self.soil_height.x_max,
            self.soil_height.grid_size_x,
            device=self.device,
        )
        y_points = torch.linspace(
            self.soil_height.y_min,
            self.soil_height.y_max,
            self.soil_height.grid_size_y,
            device=self.device,
        )

        # Create meshgrid for querying
        points_x, points_y = torch.meshgrid(x_points, y_points, indexing="ij")

        # Reshape for get_height call: [1, nx*ny]
        query_x = points_x.reshape(1, -1)
        query_y = points_y.reshape(1, -1)

        # Get heights for all environments
        heights = self.soil_height.get_height(query_x, query_y)

        # Reshape back to [n_envs, nx, ny]
        heights = heights.reshape(
            self.n_envs, self.soil_height.grid_size_x, self.soil_height.grid_size_y
        )

        # Select environments if specified
        if idx is not ...:
            heights = heights[idx]

        # Handle case where heights is empty after indexing
        if heights.numel() == 0:
            return (
                torch.zeros(0, device=self.device),
                torch.zeros(0, device=self.device),
            )

        # Calculate min and max along spatial dimensions
        heights_flat = heights.reshape(heights.shape[0], -1)
        return torch.min(heights_flat, dim=1)[0], torch.max(heights_flat, dim=1)[0]

    def get_soil_height_at_pos(self, x, y, env_ids=...):
        """Get soil height at specified x positions.

        Args:
            x: x tensor to query [n_envs, n_points]
            y: y tensor to query [n_envs, n_points]
            env_ids: Optional environment indices

        Returns:
            Tensor of soil heights
        """
        return self.soil_height.get_height(x, y, env_ids)

    def get_max_depth_height(self):
        return self.max_depth_height.get_height_field()

    def get_max_depth_height_at_pos(self, x, y, env_ids=...):
        """Get maximum depth height at specified points.

        Args:
            x: x positions to query [n_envs, n_points]
            y: y positions to query [n_envs, n_points]
            env_ids: Optional environment indices

        Returns:
            Tensor of maximum depth heights
        """
        return self.max_depth_height.get_height(x, y, env_ids)

    def get_soil_angle_at_pos(self, x, y, env_ids=...):
        return self.soil_height.get_angle_to_world(x, y, env_ids)

    def get_visualization_data(self):
        """Get visualization data for the soil model.

        Returns:
            dict: Dictionary containing visualization data including:
                - forces: Resultant forces on bucket
                - moments: Resultant moments on bucket
                - bucket_pos: Bucket center of mass position
                - soil_profile: Cross-section of soil at current bucket y-position
                - soil_normals: Soil normal vectors at key points
        """
        vis_data = {
            "forces": self.get_resultant_force(),
            "moments": self.get_resultant_moment(),
            "bucket_pos": self.bucket_com_pos_w,
            "soil_profile": None,
            "soil_normals": None,
        }

        # Get soil profile data
        if hasattr(self, "soil_height"):
            # Create a grid of x points for visualization
            num_x_points = 100
            x_points = torch.linspace(
                self.soil_height.x_min,
                self.soil_height.x_max,
                num_x_points,
                device=self.device,
            )

            # Get heights at these points for the first environment
            y_points = torch.full_like(
                x_points, self.bucket_pos_w[0, 1]
            )  # Use current bucket y-position
            heights = self.soil_height.get_height(
                x_points.unsqueeze(0), y_points.unsqueeze(0)
            )

            # Store profile data
            vis_data["soil_profile"] = {
                "x": x_points.cpu().numpy(),
                "y": y_points.cpu().numpy(),
                "heights": heights[0].cpu().numpy(),  # Use first environment
            }

        # Get soil normal data if available
        if hasattr(self, "soil_normal_futures"):
            vis_data["soil_normals"] = {
                "angles": self.soil_normal_futures[0]
                .cpu()
                .numpy(),  # Use first environment
                "positions": self.bucket_pos_w[0]
                .cpu()
                .numpy(),  # Use first environment
            }

        return vis_data

    def get_ray_observations(self):
        """Get height observations at specified ray positions.

        Returns:
            Tensor of heights [n_envs, num_rays_x * num_rays_y]
        """
        num_rays_x = self.cfg.soil_model_cfg.model_3d.num_rays_x
        num_rays_y = self.cfg.soil_model_cfg.model_3d.num_rays_y

        # Create coordinate grids for all environments at once
        x_range = torch.linspace(
            self.cfg.soil_height.x_min,
            self.cfg.soil_height.x_max,
            num_rays_x,
            device=self.device,
        )
        y_range = torch.linspace(-2.0, 2.0, num_rays_y, device=self.device)

        # Create meshgrid for all environments
        x_grid, y_grid = torch.meshgrid(x_range, y_range, indexing="ij")

        # Reshape for get_height call: [n_envs, num_rays_x * num_rays_y]
        query_x = x_grid.reshape(1, -1).expand(self.n_envs, -1)
        query_y = y_grid.reshape(1, -1).expand(self.n_envs, -1)

        # Get heights for all environments at once
        heights = self.soil_height.get_height(query_x, query_y)

        return heights

    # ----- soil parameters -----
    def get_soil_failure_ang(self):
        """Get the soil failure angle.

        Returns:
            Tensor containing soil failure angles
        """
        return self.soil_parameters.phi

    def get_ssp_ang_to_soil(self):
        """Get the angle of the secondary separation plate relative to soil.

        Returns:
            Tensor containing angles
        """
        return self.ssp.beta

    def get_ssp_L(self):
        """Get the length of the secondary separation plate.

        Returns:
            Tensor containing lengths
        """
        return self.ssp.L

    def get_ssp_L_max(self):
        """Get the maximum length of the secondary separation plate.

        Returns:
            Tensor containing maximum lengths
        """
        return self.ssp.L_max

    def get_ssp_L_max_no_bucket(self):
        """Get the maximum length of the SSP without considering the bucket.

        Returns:
            Tensor containing maximum lengths
        """
        return self.ssp.L_max_no_bucket

    def get_resultant_force(self):
        """Get the resultant soil force on the bucket.

        Returns:
            Tensor of resultant forces [n_envs, 3]
        """
        if hasattr(self.forces, "resultant_force"):
            return self.forces.resultant_force
        else:
            # Return zero forces if not calculated
            return torch.zeros((self.n_envs, 3), device=self.device)

    def get_resultant_moment(self):
        """Get the resultant soil moment on the bucket.

        Returns:
            Tensor of resultant moments [n_envs, 3]
        """
        if hasattr(self.forces, "resultant_moment"):
            return self.forces.resultant_moment
        else:
            # Return zero moments if not calculated
            return torch.zeros((self.n_envs, 3), device=self.device)

    # ----- bucket parameters -----
    def get_fill_ratio(self):
        """Return the current bucket fill ratio."""
        return self.bucket_state.fill_ratio

    def is_state_invalid(self, idxs=...):
        """Check if the soil state is invalid.

        Args:
            idxs: Indices of environments to check

        Returns:
            Boolean tensor indicating if each environment's state is invalid
        """
        # Check for NaN or Inf in height field
        hf = self.soil_height.height_field[idxs]
        is_invalid = torch.any(~torch.isfinite(hf), dim=(1, 2)).unsqueeze(1)

        # Check for heights outside reasonable bounds
        min_height, max_height = self.get_min_max_soil_height(idxs)
        height_range = max_height - min_height
        # is_invalid_height_range = (
        #     (height_range > self.cfg.soil_model_cfg.model_3d.max_allowed_height_range).unsqueeze(1)
        #     if hasattr(self.cfg.soil_model_cfg.model_3d, "max_allowed_height_range")
        #     else self.false_vec[idxs]
        # )
        # is_invalid = torch.logical_or(is_invalid, is_invalid_height_range)

        is_invalid = torch.logical_or(
            is_invalid,
            (
                (
                    height_range
                    > self.cfg.soil_model_cfg.model_3d.max_allowed_height_range
                ).unsqueeze(1)
                if hasattr(self.cfg.soil_model_cfg.model_3d, "max_allowed_height_range")
                else self.false_vec[idxs]
            ),
        )

        # Only check SSP conditions when bucket is in soil
        in_soil = self.bucket_state.depth[idxs] > 0.0

        # Check SSP-related conditions with more lenient bounds
        too_flat_ssp = (
            self.get_ssp_L()[idxs] > self.get_ssp_L_max_no_bucket()[idxs] * 1.5
        )  # Allow 50% more
        too_long_ssp = (
            self.get_ssp_L()[idxs] > self.get_ssp_L_max()[idxs] * 1.5
        )  # Allow 50% more

        # Allow wider range for SSP angle to soil
        invalid_ssp_beta = torch.logical_or(
            self.get_ssp_ang_to_soil()[idxs] <= -0.1,  # Allow slightly negative angles
            self.get_ssp_ang_to_soil()[idxs]
            > np.pi + 0.1,  # Allow slightly larger angles
        )

        # Allow slightly negative soil failure angles
        invalid_ssp_rho = self.get_soil_failure_ang()[idxs] <= -0.1

        # Only apply SSP-related checks when bucket is in soil
        ssp_invalid = torch.where(
            in_soil,
            (too_flat_ssp | too_long_ssp | invalid_ssp_beta | invalid_ssp_rho),
            self.false_vec[idxs],
        )

        is_invalid = torch.logical_or(is_invalid, ssp_invalid)
        # if torch.any(is_invalid):
        #     print("[DEBUG] State invalid due to:")
        #     print(f"  Too flat SSP: {torch.any(too_flat_ssp)}")
        #     print(f"  Too long SSP: {torch.any(too_long_ssp)}")
        #     print(f"  Invalid SSP angle: {torch.any(invalid_ssp_beta)}")
        #     print(f"  Invalid soil failure angle: {torch.any(invalid_ssp_rho)}")
        #     print(f"  Invalid height range: {torch.any(is_invalid_height_range)}") # This variable was also part of the commented block
        return is_invalid

    def get_bucket_depth(self):
        """Get the current bucket depth in soil.

        Returns:
            Tensor containing bucket depths [n_envs, 1]
        """
        return self.bucket_state.depth

    def get_bucket_depth_clipped(self):
        """Get the current bucket depth in soil, clipped to be positive.

        Returns:
            Tensor containing clipped bucket depths [n_envs, 1]
        """
        return self.bucket_state.clipped_depth

    def set_bucket_fill_state(self, state, env_ids=..., is_ratio=True):
        """Set the bucket fill state.

        Args:
            state: Fill state to set (ratio or area)
            env_ids: Indices of environments to update
            is_ratio: If True, state is a ratio [0-1], otherwise an area
        """
        self.bucket_state.set_fill_state(state, env_ids, is_ratio)

    def get_max_fill_area(self):
        """Get the maximum fill area of the bucket.

        Returns:
            Tensor containing maximum fill areas
        """
        return self.bucket_state.max_fill_area

    def get_fill_area(self):
        """Get the current fill area of the bucket.

        Returns:
            Tensor containing fill areas
        """
        return self.bucket_state.fill_area

    def get_swept_area(self):
        """Get the swept area of the bucket.

        Returns:
            Tensor containing swept areas
        """
        return self.bucket_state.swept_area

    def get_soil_volume_per_segment(self):
        """Get the soil volume per Y-segment for 3D analysis.

        Returns:
            Tensor containing soil volumes per segment [n_envs, num_y_segments]
        """
        if hasattr(self.bucket_state, 'soil_volume_per_segment'):
            return self.bucket_state.soil_volume_per_segment
        else:
            # Fallback for backward compatibility
            return torch.zeros((self.n_envs, 50), device=self.device)

    def get_total_soil_volume(self):
        """Get the total soil volume in the bucket.

        Returns:
            Tensor containing total soil volumes [n_envs, 1]
        """
        if hasattr(self.bucket_state, 'total_soil_volume'):
            return self.bucket_state.total_soil_volume
        else:
            # Fallback: calculate from fill_area
            return self.bucket_state.fill_area * self.bucket_state.b

    def get_bucket_vel_cos(self):
        """Get the cosine of the angle between bucket velocity and bottom plate normal.

        Returns:
            Tensor containing cosine values
        """
        return self.bucket_state.vel_cos

    def get_bucket_com_pos_w(self):
        """Get the center of mass position of the bucket in world frame.

        Returns:
            Tensor containing bucket COM positions
        """
        return self.bucket_state.COM_pos_w

    def get_bucket_full_angle_w(self):
        """Get the angle between the full bucket and the horizon.

        Returns:
            Tensor containing angles
        """
        return self.bucket_state.full_angle_to_horizon

    def get_bucket_bp_angle_w(self):
        """Get the angle between the bottom plate and the horizon.

        Returns:
            Tensor containing angles
        """
        return self.bucket_state.bp_angle_to_horizon

    # ----- measurements -----
    def _update_measurements(self):
        """Fetch measurements from environment.

        This method updates all the measurement buffers with data from the environment.
        These measurements are the only external data needed by the soil model.
        """
        with torch.profiler.record_function("Soil3D._update_measurements"):
            # Update 3D measurements first (primary representation)
            self.bucket_pos_w[:] = self.env.m545_measurements.bucket_pos_w
            self.prev_bucket_pos_w[:] = self.env.m545_measurements.prev_bucket_pos_w
            self.bucket_com_pos_w[:] = self.env.m545_measurements.bucket_com_pos_w
            self.bp_unit_vector_w[:] = self.env.m545_measurements.bp_unit_vector_w
            self.bucket_vel_w[:] = self.env.m545_measurements.bucket_vel_w

    def _update_soil_height(self):
        """Update soil height field based on bucket position and shape.
        Vectorized implementation.
        """
        with torch.profiler.record_function("Soil3D._update_soil_height"):

            # Mask for active environments (bucket depth > 0)
            bucket_depth_flat = self.bucket_state.depth.view(self.n_envs)
            active_envs_mask = bucket_depth_flat > 0.0

            num_active_envs = torch.sum(active_envs_mask).item()
            if num_active_envs == 0:
                return

            active_env_indices = torch.where(active_envs_mask)[0]

            # Reset processed_cells for active environments for this update cycle
            # self.processed_cells is [n_envs, grid_size_x, grid_size_y]
            # Create a view or clone for the active environments and set to False
            # This ensures that for the current update step, all cells are considered not yet processed for active envs.
            # Note: This assumes self.processed_cells should be reset for each call to _update_soil_height for the envs being processed.
            # If only a subset of grid cells related to active_envs_mask needs reset, a more targeted approach would be needed,
            # but resetting the entire slice for active_envs_mask is equivalent to the original loop's env_id scope.
            self.processed_cells[active_envs_mask] = False

            # Get relevant data for active environments
            bucket_pos = self.bucket_pos_w[active_envs_mask]  # [num_active_envs, 3]
            # Ensure bucket_width and bp_length are correctly shaped as [num_active_envs, 1]
            # Assuming self.bucket_state.b and self.bucket_state.a could be scalar or [n_envs] or [n_envs,1]

            raw_bucket_width = self.bucket_state.b
            if isinstance(raw_bucket_width, torch.Tensor):
                if raw_bucket_width.ndim == 0:  # scalar tensor
                    bucket_width = raw_bucket_width.expand(num_active_envs, 1)
                elif raw_bucket_width.shape[0] == self.n_envs:  # [n_envs] or [n_envs,1]
                    bucket_width = raw_bucket_width[active_envs_mask]
                    if bucket_width.ndim == 1:
                        bucket_width = bucket_width.unsqueeze(1)
                else:  # Assumed to be already [num_active_envs, 1] or similar compatible
                    bucket_width = raw_bucket_width
            else:  # Python float/int from cfg, .item() in original code
                bucket_width = torch.full(
                    (num_active_envs, 1),
                    raw_bucket_width,
                    device=self.device,
                    dtype=torch.float32,
                )

            raw_bp_length = self.bucket_state.a
            if isinstance(raw_bp_length, torch.Tensor):
                if raw_bp_length.ndim == 0:  # scalar tensor
                    bp_length = raw_bp_length.expand(num_active_envs, 1)
                elif raw_bp_length.shape[0] == self.n_envs:
                    bp_length = raw_bp_length[active_envs_mask]
                    if bp_length.ndim == 1:
                        bp_length = bp_length.unsqueeze(1)
                else:
                    bp_length = raw_bp_length
            else:  # Python float/int from cfg
                bp_length = torch.full(
                    (num_active_envs, 1),
                    raw_bp_length,
                    device=self.device,
                    dtype=torch.float32,
                )

            bp_vector = self.bp_unit_vector_w[active_envs_mask]  # [num_active_envs, 3]

            # Bucket orientation vectors
            # bp_normal = bp_vector / (
            #     torch.norm(bp_vector, dim=1, keepdim=True) + 1e-6
            # )  # [num_active_envs, 3]

            bp_width_vector = torch.stack(
                [
                    bp_vector[:, 1],
                    -bp_vector[:, 0],
                    torch.zeros(num_active_envs, device=self.device),
                ],
                dim=1,
            )  # [num_active_envs, 3]
            bp_width_vector = bp_width_vector / (
                torch.norm(bp_width_vector, dim=1, keepdim=True) + 1e-6
            )

            # Grid dimensions
            grid_size_x = self.soil_height.grid_size_x
            grid_size_y = self.soil_height.grid_size_y
            x_min, x_max = self.soil_height.x_min, self.soil_height.x_max
            y_min, y_max = self.soil_height.y_min, self.soil_height.y_max

            cell_size_x = (x_max - x_min) / (grid_size_x - 1)
            cell_size_y = (y_max - y_min) / (grid_size_y - 1)

            # Bucket corners
            front_mid = bucket_pos  # [num_active_envs, 3]
            back_mid = bucket_pos + bp_vector * bp_length  # [num_active_envs, 3]

            half_width_scaled = bp_width_vector * (
                bucket_width / 2
            )  # [num_active_envs, 3]
            front_left = front_mid - half_width_scaled
            front_right = front_mid + half_width_scaled
            back_left = back_mid - half_width_scaled
            back_right = back_mid + half_width_scaled

            corners = torch.stack(
                [front_left, front_right, back_left, back_right], dim=1
            )  # [num_active_envs, 4, 3]

            # Convert corners to grid indices
            # Using .div instead of / for potentially better control if cell_size could be zero (though unlikely here)
            corner_x_grid = (corners[..., 0] - x_min).div(cell_size_x)
            corner_y_grid = (corners[..., 1] - y_min).div(cell_size_y)

            margin = 2  # Extra cells margin

            min_x_idx = torch.clamp(
                torch.floor(torch.min(corner_x_grid, dim=1)[0] - margin),
                0,
                grid_size_x - 1,
            ).long()
            max_x_idx = torch.clamp(
                torch.ceil(torch.max(corner_x_grid, dim=1)[0] + margin),
                0,
                grid_size_x - 1,
            ).long()
            min_y_idx = torch.clamp(
                torch.floor(torch.min(corner_y_grid, dim=1)[0] - margin),
                0,
                grid_size_y - 1,
            ).long()
            max_y_idx = torch.clamp(
                torch.ceil(torch.max(corner_y_grid, dim=1)[0] + margin),
                0,
                grid_size_y - 1,
            ).long()

            # Calculate max dimensions for the padded grid across active environments
            # Add 1 because ranges are inclusive for arange
            max_x_dim = torch.max(max_x_idx - min_x_idx + 1).item()
            max_y_dim = torch.max(max_y_idx - min_y_idx + 1).item()

            if (
                max_x_dim <= 0 or max_y_dim <= 0
            ):  # No cells to process if any dimension is zero or negative
                return

            ii = (
                torch.arange(max_x_dim, device=self.device)
                .view(1, -1, 1)
                .expand(num_active_envs, -1, max_y_dim)
            )
            jj = (
                torch.arange(max_y_dim, device=self.device)
                .view(1, 1, -1)
                .expand(num_active_envs, max_x_dim, -1)
            )

            global_i = min_x_idx.view(-1, 1, 1) + ii
            global_j = min_y_idx.view(-1, 1, 1) + jj

            valid_cell_mask = (global_i <= max_x_idx.view(-1, 1, 1)) & (
                global_j <= max_y_idx.view(-1, 1, 1)
            )

            # Further filter global_i, global_j, and other tensors based on valid_cell_mask to reduce computation size.
            # This can be done by torch.where(valid_cell_mask) and then gathering.
            # However, this makes indexing more complex. For now, continue with full max_dim grid and mask at the end.

            x_world = x_min + global_i * cell_size_x
            y_world = y_min + global_j * cell_size_y

            bucket_pos_expanded = bucket_pos.view(num_active_envs, 1, 1, 3)
            bp_vector_expanded = bp_vector.view(num_active_envs, 1, 1, 3)
            bp_width_vector_expanded = bp_width_vector.view(num_active_envs, 1, 1, 3)
            bp_length_expanded = bp_length.view(num_active_envs, 1, 1, 1)
            bucket_width_expanded = bucket_width.view(num_active_envs, 1, 1, 1)

            vec_to_point_x = x_world - bucket_pos_expanded[..., 0]
            vec_to_point_y = y_world - bucket_pos_expanded[..., 1]

            proj_length = (
                vec_to_point_x * bp_vector_expanded[..., 0]
                + vec_to_point_y * bp_vector_expanded[..., 1]
            )

            proj_width = (
                vec_to_point_x * bp_width_vector_expanded[..., 0]
                + vec_to_point_y * bp_width_vector_expanded[..., 1]
            )

            is_inside_mask = (
                valid_cell_mask
                & (proj_length >= 0)
                & (proj_length <= bp_length_expanded.squeeze(-1))
                & (torch.abs(proj_width) <= bucket_width_expanded.squeeze(-1) / 2)
            )

            # Mask out cells that have already been processed in this update cycle for their respective environment
            # Gather from self.processed_cells based on active_env_indices, global_i, global_j
            # This step assumes global_i and global_j are within bounds [0, grid_size_x-1] and [0, grid_size_y-1]
            # which they should be due to clamping of min/max_idx and valid_cell_mask construction.

            # Create indices for gathering from self.processed_cells
            # Need to be careful if a cell is outside an env's actual bounding box but inside max_x_dim box
            # is_inside_mask already incorporates valid_cell_mask, so we only care about potentially valid cells.

            env_idx_for_gather, i_idx_for_gather, j_idx_for_gather = torch.where(
                is_inside_mask
            )
            if env_idx_for_gather.numel() > 0:
                actual_env_ids_for_gather = active_env_indices[env_idx_for_gather]
                actual_i_for_gather = global_i[is_inside_mask]
                actual_j_for_gather = global_j[is_inside_mask]

                already_processed_values = self.processed_cells[
                    actual_env_ids_for_gather, actual_i_for_gather, actual_j_for_gather
                ]

                # Create a mask of the same shape as is_inside_mask, default to False
                not_yet_processed_expanded_mask = torch.zeros_like(
                    is_inside_mask, dtype=torch.bool
                )
                # Place the gathered `~already_processed_values` into this mask at the correct locations
                not_yet_processed_expanded_mask[is_inside_mask] = (
                    ~already_processed_values
                )

                is_inside_mask &= not_yet_processed_expanded_mask

            if torch.any(is_inside_mask):
                bp_len_sq = bp_length_expanded.squeeze(-1)
                t = torch.zeros_like(proj_length)
                # safe_bp_len_mask = bp_len_sq > 1e-6 # Unused variable
                # Apply safe_bp_len_mask to the indexing of t and proj_length/bp_len_sq
                # to prevent operating on all elements if only a few are safe.

                # Consider only where is_inside_mask is True for t calculation
                proj_length_masked = proj_length[is_inside_mask]
                # Expand bp_len_sq to match is_inside_mask's shape before boolean indexing
                bp_len_sq_expanded_for_mask = bp_len_sq.expand_as(is_inside_mask)
                bp_len_sq_masked = bp_len_sq_expanded_for_mask[is_inside_mask]

                safe_bp_len_mask_dynamic = bp_len_sq_masked > 1e-6

                t_masked = torch.zeros_like(proj_length_masked)
                if torch.any(safe_bp_len_mask_dynamic):
                    t_masked[safe_bp_len_mask_dynamic] = (
                        proj_length_masked[safe_bp_len_mask_dynamic]
                        / bp_len_sq_masked[safe_bp_len_mask_dynamic]
                    )

                t_masked = torch.clamp(t_masked, 0, 1)
                t[is_inside_mask] = (
                    t_masked  # Place calculated t back into the full tensor
                )

                front_mid_exp = front_mid.view(num_active_envs, 1, 1, 3)
                back_mid_exp = back_mid.view(num_active_envs, 1, 1, 3)

                interp_pos_on_bucket_plane = (
                    1 - t.unsqueeze(-1)
                ) * front_mid_exp + t.unsqueeze(-1) * back_mid_exp
                bucket_height_at_cell = interp_pos_on_bucket_plane[..., 2]

                thickness_offset = 0.02

                update_env_idx, update_i_local_idx, update_j_local_idx = torch.where(
                    is_inside_mask
                )

                if update_env_idx.numel() > 0:
                    actual_env_ids = active_env_indices[update_env_idx]
                    actual_i_global = global_i[
                        update_env_idx, update_i_local_idx, update_j_local_idx
                    ]
                    actual_j_global = global_j[
                        update_env_idx, update_i_local_idx, update_j_local_idx
                    ]

                    current_soil_height = self.soil_height.height_field[
                        actual_env_ids, actual_i_global, actual_j_global
                    ]
                    max_depth_val = self.max_depth_height.height_field[
                        actual_env_ids, actual_i_global, actual_j_global
                    ]

                    bucket_height_for_update = bucket_height_at_cell[
                        update_env_idx, update_i_local_idx, update_j_local_idx
                    ]

                    update_condition = (
                        bucket_height_for_update < current_soil_height
                    ) & (bucket_height_for_update > max_depth_val)

                    if torch.any(update_condition):
                        new_height = bucket_height_for_update + thickness_offset
                        min_allowed_height = max_depth_val + 0.05
                        new_height = torch.max(new_height, min_allowed_height)

                        final_new_height = torch.where(
                            update_condition, new_height, current_soil_height
                        )
                        self.soil_height.height_field[
                            actual_env_ids, actual_i_global, actual_j_global
                        ] = final_new_height

                        self.processed_cells[
                            actual_env_ids, actual_i_global, actual_j_global
                        ] = True

    # def plot_state(self, idx, show=True, label=True, ax=None):
    #     """Plot the state of the 3D soil model for selected environments (XZ slice).
    #
    #     Plots a 2D slice of the simulation state in the XZ plane,
    #     taken at the current Y position of the bucket for each specified environment.
    #     """
    #     if ax is None:
    #         fig, ax = plt.subplots()
    #     else:
    #         fig = ax.figure  # Get the figure associated with the axes
    #
    #     # Ensure idx is iterable
    #     if isinstance(idx, int):
    #         idx = [idx]
    #
    #     # Define X coordinates for plotting soil profile
    #     x_plot_coords = torch.linspace(
    #         self.soil_height.x_min,
    #         self.soil_height.x_max,
    #         self.soil_height.grid_size_x,  # Use grid resolution
    #         device=self.device,
    #     )
    #
    #     for i in idx:
    #         # Get bucket's current Y position for slicing
    #         bucket_y = self.bucket_pos_w[i, 1].item()
    #         y_plot_coords = torch.full_like(x_plot_coords, bucket_y)
    #
    #         # Get soil height slice at bucket's Y
    #         z_soil = (
    #             self.soil_height.get_height(
    #                 x_plot_coords.unsqueeze(0), y_plot_coords.unsqueeze(0), env_ids=i
    #             )
    #             .squeeze()
    #             .cpu()
    #             .numpy()
    #         )
    #
    #         # Get max depth slice at bucket's Y
    #         z_max_depth = (
    #             self.max_depth_height.get_height(
    #                 x_plot_coords.unsqueeze(0), y_plot_coords.unsqueeze(0), env_ids=i
    #             )
    #             .squeeze()
    #             .cpu()
    #             .numpy()
    #         )
    #
    #         # Plot soil height
    #         ax.plot(
    #             x_plot_coords.cpu().numpy(),
    #             z_soil,
    #             label=f"Soil (Env {i}, Y={bucket_y:.2f})" if label else "",
    #         )
    #         # Plot max depth
    #         ax.plot(
    #             x_plot_coords.cpu().numpy(),
    #             z_max_depth,
    #             label="Max Depth" if label else "",
    #             linestyle="--",
    #             color="grey",
    #         )
    #
    #         # BUCKET (XZ Projection)
    #         tip_x = self.bucket_pos_w[i, 0].item()
    #         tip_z = self.bucket_pos_w[i, 2].item()
    #         bp_vec_x = self.bp_unit_vector_w[i, 0].item()
    #         bp_vec_z = self.bp_unit_vector_w[i, 2].item()
    #         bp_length = self.bucket_state.a.item()
    #
    #         end_x = tip_x + bp_vec_x * bp_length
    #         end_z = tip_z + bp_vec_z * bp_length
    #         ax.plot(
    #             [tip_x, end_x],
    #             [tip_z, end_z],
    #             color="k",
    #             marker="x",
    #             markevery=[0],
    #             linewidth=1.0,
    #             label="Bucket Bottom Plate" if label else "",
    #         )
    #
    #         # Bucket back position
    #         back_x = self.bucket_state.back_pos_w[i, 0].item()
    #         back_z = self.bucket_state.back_pos_w[i, 1].item()
    #         ax.scatter(
    #             back_x,
    #             back_z,
    #             color="k",
    #             marker="o",
    #             s=30,
    #             label="Bucket Back" if label else "",
    #         )
    #
    #         # Bucket part in soil (visual approximation using bp_soil length on XZ projection)
    #         # Note: bp_soil might be calculated based on 3D intersection, projection is approximate
    #         bp_soil_len = self.bucket_state.bp_soil[i].item()
    #         end_soil_x = tip_x + bp_vec_x * bp_soil_len
    #         end_soil_z = tip_z + bp_vec_z * bp_soil_len
    #         ax.plot(
    #             [tip_x, end_soil_x],
    #             [tip_z, end_soil_z],
    #             color="r",
    #             linewidth=2.5,
    #             label="BP in Soil (Proj.)" if label else "",
    #         )
    #
    #         # SSP (XZ Projection)
    #         ssp_vec_x = self.ssp.ssp_unit_vector_w[i, 0].item()
    #         ssp_vec_z = self.ssp.ssp_unit_vector_w[i, 2].item()
    #         ssp_L = self.ssp.L[i].item()
    #         ssp_L_max = self.ssp.L_max[i].item()
    #         # ssp_L_over_max = self.ssp.L_over_max.item() # L_over_max might not exist or be relevant in 3D
    #
    #         # SSP Max Length (Projection)
    #         end_x_max = tip_x + ssp_vec_x * ssp_L_max
    #         end_z_max = tip_z + ssp_vec_z * ssp_L_max
    #         ax.plot(
    #             [tip_x, end_x_max],
    #             [tip_z, end_z_max],
    #             color="g",
    #             linestyle="--",
    #             linewidth=1.0,
    #             label="SSP Max Length (Proj.)" if label else "",
    #         )
    #
    #         # SSP Current Length (Projection)
    #         end_x_ssp = tip_x + ssp_vec_x * ssp_L
    #         end_z_ssp = tip_z + ssp_vec_z * ssp_L
    #         ax.plot(
    #             [tip_x, end_x_ssp],
    #             [tip_z, end_z_ssp],
    #             color="g",
    #             marker="x",
    #             linewidth=1.5,
    #             label="SSP (Proj.)" if label else "",
    #         )
    #
    #         # Centroid
    #         ax.scatter(
    #             self.ssp.centroid_w_x[i].item(),
    #             self.ssp.centroid_w_z[i].item(),
    #             marker="x",
    #             s=50,
    #             color="purple",
    #             label="Centroid" if label else "",
    #         )
    #
    #         # COM
    #         ax.scatter(
    #             self.bucket_com_pos_w[i, 0].item(),
    #             self.bucket_com_pos_w[i, 2].item(),
    #             marker="p",
    #             s=50,
    #             color="orange",
    #             label="Bucket COM" if label else "",
    #         )
    #
    #         # FEE Force Direction (XZ Projection)
    #         # Apply force at midpoint of current SSP (projected)
    #         force_app_x = tip_x + ssp_vec_x * ssp_L / 2.0
    #         force_app_z = tip_z + ssp_vec_z * ssp_L / 2.0
    #         force_dir_x = self.forces.Rs_unit_vector_w[i, 0].item()
    #         force_dir_z = self.forces.Rs_unit_vector_w[i, 2].item()
    #         # Plot a small vector representing the force direction
    #         force_viz_scale = 0.5  # Adjust scale as needed
    #         start_x = force_app_x - force_dir_x * force_viz_scale
    #         start_z = force_app_z - force_dir_z * force_viz_scale
    #         ax.plot(
    #             [start_x, force_app_x],
    #             [start_z, force_app_z],
    #             color="b",
    #             linewidth=1.5,
    #             marker="<",
    #             markevery=[0],
    #             label="FEE Force Dir (Proj.)" if label else "",
    #         )
    #
    #         # Soil Failure Wedge (XZ Projection)
    #         # Use angles rho and beta, apply rotation in XZ plane to projected SSP vector
    #         ssp_vec_xz = self.ssp.ssp_unit_vector_w[i, [0, 2]].cpu().numpy()
    #         # Ensure ssp_vec_xz is not zero vector before normalizing
    #         norm_ssp_vec_xz = np.linalg.norm(ssp_vec_xz)
    #         if norm_ssp_vec_xz > 1e-6:
    #             ssp_vec_xz_norm = ssp_vec_xz / norm_ssp_vec_xz
    #         else:
    #             ssp_vec_xz_norm = np.array(
    #                 [1.0, 0.0]
    #             )  # Default to X-axis if norm is zero
    #
    #         rho_i = self.forces.rho[i].item()
    #         beta_i = self.ssp.beta[i].item()
    #
    #         # Check for valid angles
    #         if abs(np.sin(rho_i)) > 1e-6:  # Use tolerance for float comparison
    #             # Failure plane line
    #             angle_to_rotate_fail = np.pi - rho_i - beta_i
    #             rot_mat_fail = rot_mat_2d(angle_to_rotate_fail)
    #             rotated_ssp_fail = (
    #                 rot_mat_fail @ ssp_vec_xz_norm
    #             )  # Rotate normalized direction
    #
    #             L_fail = (
    #                 self.ssp.L[i]
    #                 * torch.sin(self.ssp.beta[i])
    #                 / torch.sin(self.forces.rho[i])
    #             ).item()
    #             failure_plane_vec_xz = rotated_ssp_fail * L_fail
    #
    #             wedge_top_x = tip_x + failure_plane_vec_xz[0]
    #             wedge_top_z = tip_z + failure_plane_vec_xz[1]
    #
    #             ax.plot(
    #                 [tip_x, wedge_top_x],
    #                 [tip_z, wedge_top_z],
    #                 color="C1",
    #                 linestyle="-",
    #                 linewidth=1.0,
    #                 label="Failure Wedge (Proj.)" if label else "",
    #             )
    #
    #             # Linear terrain approximation line
    #             angle_to_rotate_terrain = -beta_i
    #             rot_mat_terrain = rot_mat_2d(angle_to_rotate_terrain)
    #             rotated_neg_ssp = rot_mat_terrain @ (
    #                 -ssp_vec_xz_norm
    #             )  # Rotate normalized negative direction
    #
    #             # Check for valid tan(rho_i)
    #             if abs(np.tan(rho_i)) > 1e-6:  # Use tolerance for float comparison
    #                 L_terrain = (
    #                     self.ssp.L[i]
    #                     * (
    #                         torch.cos(self.ssp.beta[i])
    #                         + torch.sin(self.ssp.beta[i])
    #                         / torch.tan(self.forces.rho[i])
    #                     )
    #                 ).item()
    #                 lin_terrain_vec_xz = rotated_neg_ssp * L_terrain
    #
    #                 ax.plot(
    #                     [end_x_ssp, end_x_ssp + lin_terrain_vec_xz[0]],
    #                     [end_z_ssp, end_z_ssp + lin_terrain_vec_xz[1]],
    #                     color="C1",
    #                     linestyle="-",
    #                     linewidth=1.0,
    #                 )
    #             else:
    #                 # Handle case where tan(rho_i) is zero (rho_i is 0 or pi) - Skip drawing terrain line?
    #                 # if self.print_debug:
    #                 #     print(
    #                 #         f"[WARN] plot_state Env {i}: rho angle is close to 0 or pi, skipping terrain line."
    #                 #     )
    #                 pass # Explicitly pass if print_debug is removed
    #
    #             # Wedge centroid depth indicator
    #             # forces.z is likely the depth relative to the top of the wedge
    #             centroid_depth_z = wedge_top_z - self.forces.z[i].item()
    #             # Plot horizontal line at centroid depth, extending slightly
    #             ax.plot(
    #                 [wedge_top_x - 0.2, wedge_top_x + 0.2],
    #                 [centroid_depth_z, centroid_depth_z],
    #                 label="Wedge Centroid Z" if label else "",
    #                 color="magenta",
    #                 linestyle=":",
    #                 linewidth=1.0,
    #             )
    #             # Vertical line from top to centroid depth
    #             ax.plot(
    #                 [wedge_top_x, wedge_top_x],
    #                 [wedge_top_z, centroid_depth_z],
    #                 color="magenta",
    #                 linestyle=":",
    #                 linewidth=1.0,
    #             )
    #         else:
    #             # if self.print_debug:
    #             #     print(
    #             #         f"[WARN] plot_state Env {i}: rho angle is close to 0 or pi, skipping wedge drawing."
    #             #     )
    #             pass # Explicitly pass if print_debug is removed
    #
    #     ax.set_xlabel("X World Coordinate (m)")
    #     ax.set_ylabel("Z World Coordinate (m)")
    #
    #     # Set title based on number of environments plotted
    #     if len(idx) == 1:
    #         # Get bucket_y for the single environment
    #         single_env_id = idx[0]
    #         bucket_y_title = self.bucket_pos_w[single_env_id, 1].item()
    #         ax.set_title(
    #             f"Soil Model State (Env {single_env_id}, XZ Slice @ Y={bucket_y_title:.2f})"
    #         )
    #     else:
    #         ax.set_title(f"Soil Model State ({len(idx)} Envs, XZ Slice @ Bucket Y)")
    #
    #     ax.grid(True)
    #     ax.axis("equal")  # Maintain aspect ratio
    #     if label:
    #         # Adjust legend position if needed
    #         ax.legend(fontsize="small", loc="center left", bbox_to_anchor=(1, 0.5))
    #         fig.tight_layout(rect=[0, 0, 0.85, 1])  # Make space for legend outside
    #
    #     if show:
    #         # plt.show(block=False) # Commented out to remove plotting
    #         # # Keep plot open until user interaction if blocking is desired
    #         # # plt.show(block=True)
    #         # try:
    #         #     input("Press [Enter] in the terminal to close the plot...") # Commented out to remove input
    #         #     plt.close(fig)
    #         # except EOFError:  # Handle non-interactive environments
    #         #     plt.close(fig)
    #         pass # Explicitly pass if show logic is removed
